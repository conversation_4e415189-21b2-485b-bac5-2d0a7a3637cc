<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelBloom - Discover Your Next Adventure</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Floating Navigation -->
    <nav class="floating-nav">
        <div class="nav-container">
            <div class="brand">
                <i class="fas fa-compass brand-icon"></i>
                <span class="brand-text">TravelBloom</span>
            </div>
            <div class="nav-menu">
                <a href="#Home" class="nav-link"><i class="fas fa-home"></i> Home</a>
                <a href="#About" class="nav-link"><i class="fas fa-info-circle"></i> About</a>
                <a href="#Contactus" class="nav-link"><i class="fas fa-envelope"></i> Contact</a>
            </div>
            <div class="search-toggle">
                <button id="search-toggle-btn" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Search Overlay -->
    <div id="search-overlay" class="search-overlay">
        <div class="search-container">
            <button id="close-search" class="close-search">
                <i class="fas fa-times"></i>
            </button>
            <div class="search-content">
                <h2>Where do you want to go?</h2>
                <div class="search-box">
                    <input id="searchinput" type="text" placeholder="Search destinations, temples, beaches..." class="search-input">
                    <div class="search-buttons">
                        <button id="searchbtn" class="search-action-btn primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button id="clearbtn" class="search-action-btn secondary">
                            <i class="fas fa-eraser"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="search-results" class="search-results">
            <div id="resultContainer"></div>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="Home" class="hero-section">
        <div class="hero-background">
            <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="fas fa-globe-americas"></i>
                    <span>Discover the World</span>
                </div>
                <h1 class="hero-title">
                    <span class="title-line">Explore</span>
                    <span class="title-line highlight">Dream</span>
                    <span class="title-line">Destinations</span>
                </h1>
                <p class="hero-description">
                    Embark on extraordinary journeys to breathtaking destinations around the globe.
                    Immerse yourself in diverse cultures, explore ancient temples, relax on pristine beaches,
                    and create unforgettable memories that will last a lifetime.
                </p>
                <div class="hero-actions">
                    <button class="cta-button primary" onclick="bookNow()">
                        <i class="fas fa-plane-departure"></i>
                        Book Your Adventure
                    </button>
                    <button class="cta-button secondary" onclick="document.getElementById('search-toggle-btn').click()">
                        <i class="fas fa-search"></i>
                        Explore Destinations
                    </button>
                </div>
            </div>
            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Countries</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">200+</div>
                    <div class="stat-label">Destinations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10K+</div>
                    <div class="stat-label">Happy Travelers</div>
                </div>
            </div>
        </div>

        <!-- Social Media Sidebar -->
        <div class="social-sidebar">
            <div class="social-line"></div>
            <a href="https://twitter.com/?lang=en" target="_blank" class="social-link">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="https://www.facebook.com/" target="_blank" class="social-link">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="https://www.instagram.com/" target="_blank" class="social-link">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="https://www.youtube.com/" target="_blank" class="social-link">
                <i class="fab fa-youtube"></i>
            </a>
            <div class="social-line"></div>
        </div>
    </section>
    <!-- About Section -->
    <section id="About" class="about-section">
        <div class="container">
            <div class="about-grid">
                <div class="about-content">
                    <div class="section-badge">
                        <i class="fas fa-heart"></i>
                        <span>Our Story</span>
                    </div>
                    <h2 class="section-title">About TravelBloom</h2>
                    <p class="about-text">
                        Welcome to TravelBloom! We are passionate travel experts dedicated to creating
                        extraordinary journeys for adventurous souls. Our mission is to connect travelers
                        with authentic cultural experiences, breathtaking destinations, and unforgettable
                        memories around the globe.
                    </p>
                    <p class="about-text">
                        With years of expertise in curating personalized travel experiences, we specialize
                        in destinations across countries, temples, and beaches. We believe in sustainable
                        tourism, cultural respect, and creating meaningful connections between travelers
                        and local communities.
                    </p>
                    <div class="about-features">
                        <div class="feature-item">
                            <i class="fas fa-map-marked-alt"></i>
                            <span>Expert Local Guides</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-leaf"></i>
                            <span>Sustainable Tourism</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Cultural Immersion</span>
                        </div>
                    </div>
                </div>
                <div class="about-visual">
                    <div class="about-card">
                        <div class="card-icon">
                            <i class="fas fa-globe-asia"></i>
                        </div>
                        <h3>Global Reach</h3>
                        <p>Connecting you to amazing destinations worldwide</p>
                    </div>
                    <div class="about-card">
                        <div class="card-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <h3>Premium Service</h3>
                        <p>Award-winning travel experiences tailored for you</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact Section -->
    <section id="Contactus" class="contact-section">
        <div class="container">
            <div class="contact-grid">
                <div class="contact-info">
                    <div class="section-badge">
                        <i class="fas fa-paper-plane"></i>
                        <span>Get In Touch</span>
                    </div>
                    <h2 class="section-title">Contact Us</h2>
                    <p class="contact-description">
                        Ready to start your next adventure? Get in touch with our travel experts
                        and let us help you plan the perfect journey.
                    </p>

                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-info">
                                <h4>Email Us</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="method-info">
                                <h4>Call Us</h4>
                                <p>+****************</p>
                            </div>
                        </div>
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="method-info">
                                <h4>Visit Us</h4>
                                <p>123 Travel Street, Adventure City</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container">
                    <form class="contact-form" onsubmit="handleFormSubmit(event)">
                        <div class="form-group">
                            <label for="name">Full Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <select id="subject" name="subject" required>
                                <option value="">Select a topic</option>
                                <option value="booking">Booking Inquiry</option>
                                <option value="support">Customer Support</option>
                                <option value="partnership">Partnership</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" rows="5" required
                                placeholder="Tell us about your dream destination..."></textarea>
                        </div>
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-compass"></i>
                    <span>TravelBloom</span>
                </div>
                <p class="footer-text">Making your travel dreams bloom into reality.</p>
            </div>
        </div>
    </footer>

    <script src="travelrecommendation.js"></script>
</body>
</html>
